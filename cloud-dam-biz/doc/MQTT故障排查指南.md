# MQTT故障排查指南

## 常见错误及解决方案

### 1. "Lost connection: MqttException" 错误

**错误描述：**
```
ERROR | MQTT Call: dam-client-xxx [TID: N/A] .m.i.MqttPahoMessageDrivenChannelAdapter | Lost connection: MqttException
```

**可能原因及解决方案：**

#### 原因1：网络连接不稳定
- **解决方案：**
  - 检查网络连接稳定性
  - 增加连接超时时间：`mqtt.connection.timeout: 30`
  - 启用自动重连：`mqtt.automatic.reconnect: true`

#### 原因2：MQTT Broker配置问题
- **解决方案：**
  - 验证Broker地址和端口：`tcp://**********:2883`
  - 检查用户名密码是否正确
  - 确认Broker服务正常运行

#### 原因3：客户端ID冲突
- **解决方案：**
  - 确保每个客户端使用唯一ID
  - 代码已自动添加UUID后缀避免冲突

#### 原因4：心跳超时
- **解决方案：**
  - 调整心跳间隔：`mqtt.keep.alive.interval: 60`
  - 增加恢复间隔：`mqtt.recovery.interval: 10000`

### 2. 连接建立失败

**错误描述：**
```
Connection lost (32109) - java.net.ConnectException: Connection refused
```

**解决方案：**
1. 检查MQTT Broker是否运行
2. 验证防火墙设置
3. 确认端口2883是否开放
4. 测试网络连通性：`telnet ********** 2883`

### 3. 认证失败

**错误描述：**
```
Connection lost (32104) - Not authorized to connect
```

**解决方案：**
1. 验证用户名密码是否正确
2. 检查Broker的认证配置
3. 确认用户权限设置

### 4. 订阅失败

**错误描述：**
```
Failed to subscribe to topic: xxx
```

**解决方案：**
1. 检查主题格式是否正确
2. 验证用户是否有订阅权限
3. 确认QoS设置合理（0-2）

## 配置优化建议

### 1. 连接稳定性配置

```yaml
mqtt:
  connection:
    timeout: 30  # 增加连接超时
  keep:
    alive:
      interval: 60  # 心跳间隔
  automatic:
    reconnect: true  # 启用自动重连
  max:
    inflight: 1000  # 最大未确认消息数
  recovery:
    interval: 10000  # 恢复间隔
```

### 2. 日志配置

```yaml
logging:
  level:
    cn.powerchina.bjy.link.dam.service.mqtt: DEBUG
    org.springframework.integration.mqtt: DEBUG
    org.eclipse.paho.client.mqttv3: DEBUG
```

### 3. 监控配置

启用MQTT连接监控：
- 定期检查连接状态
- 发送心跳消息
- 自动重连机制

## 调试步骤

### 1. 检查配置
```bash
# 查看当前配置
curl http://localhost:8080/admin/mqtt/status
```

### 2. 测试连接
```bash
# 使用MQTT客户端工具测试
mosquitto_pub -h ********** -p 2883 -u f5e9ddb796ad47039a21d05477f62d82 -P 877fc31bf48e4498a8fc38593d4fb3d2 -t "test/topic" -m "test message"
```

### 3. 查看日志
```bash
# 查看应用日志
tail -f logs/application.log | grep MQTT

# 查看Spring Integration日志
tail -f logs/application.log | grep "integration.mqtt"
```

### 4. 监控连接状态
```bash
# 定期检查连接状态
watch -n 5 'curl -s http://localhost:8080/admin/mqtt/status'
```

## 性能优化

### 1. 连接池配置
- 合理设置最大未确认消息数
- 调整线程池大小
- 优化超时参数

### 2. 消息处理优化
- 异步处理消息
- 批量处理机制
- 错误重试策略

### 3. 网络优化
- 使用持久连接
- 启用消息压缩
- 合理设置QoS等级

## 常用命令

### 1. 重启MQTT服务
```bash
# 通过API重启
curl -X POST http://localhost:8080/admin/mqtt/restart
```

### 2. 查看订阅主题
```bash
curl http://localhost:8080/admin/mqtt/topics/available
```

### 3. 手动订阅主题
```bash
curl -X POST "http://localhost:8080/admin/mqtt/subscribe?topic=dam/test&qos=1"
```

### 4. 发布测试消息
```bash
curl -X POST "http://localhost:8080/admin/mqtt/publish/simple?topic=dam/test&message=hello"
```

## 联系支持

如果问题仍然存在，请提供以下信息：
1. 完整的错误日志
2. MQTT配置信息
3. 网络环境描述
4. Broker版本和配置
