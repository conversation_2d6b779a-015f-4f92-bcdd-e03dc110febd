# MQTT功能集成说明

## 概述

cloud-dam-biz工程已成功集成MQTT功能，支持消息发布和订阅。系统会自动从TransportTargetApi获取可用的MQTT主题并进行订阅。

## 功能特性

- ✅ MQTT消息发布
- ✅ MQTT主题订阅/取消订阅
- ✅ 自动获取并订阅传输目标主题
- ✅ 连接状态监控
- ✅ 消息处理和路由
- ✅ REST API接口
- ✅ 配置化管理

## 配置说明

### application.yaml配置

```yaml
mqtt:
  broker:
    url: tcp://localhost:1883  # MQTT Broker地址，支持多个地址用逗号分隔
  client:
    id: dam-client  # 客户端ID前缀，实际使用时会添加UUID后缀
  username:  # MQTT用户名，如果不需要认证可以留空
  password:  # MQTT密码，如果不需要认证可以留空
  connection:
    timeout: 10  # 连接超时时间（秒）
  keep:
    alive:
      interval: 60  # 心跳间隔（秒）
  clean:
    session: true  # 是否清除会话
  automatic:
    reconnect: true  # 是否自动重连
  default:
    topic: dam/default  # 默认发布主题
    qos: 1  # 默认QoS等级
    retained: false  # 默认是否保留消息
    subscribe:
      qos: 1  # 默认订阅QoS等级
  auto:
    subscribe:
      topics: dam/data,dam/alarm  # 自动订阅的主题，多个主题用逗号分隔
```

## API接口

### 1. 发布消息

**POST** `/admin/mqtt/publish`

参数：
- topic: 主题
- message: 消息内容
- qos: QoS等级（可选，默认1）
- retained: 是否保留消息（可选，默认false）

**POST** `/admin/mqtt/publish/simple`

参数：
- topic: 主题
- message: 消息内容

### 2. 订阅主题

**POST** `/admin/mqtt/subscribe`

参数：
- topic: 主题
- qos: QoS等级（可选，默认1）

**POST** `/admin/mqtt/subscribe/simple`

参数：
- topic: 主题

### 3. 取消订阅

**POST** `/admin/mqtt/unsubscribe`

参数：
- topic: 主题

### 4. 获取可用主题

**GET** `/admin/mqtt/topics/available`

返回所有可用的传输目标MQTT主题列表。

### 5. 订阅所有可用主题

**POST** `/admin/mqtt/subscribe/all`

自动订阅所有从TransportTargetApi获取的MQTT主题。

### 6. 获取连接状态

**GET** `/admin/mqtt/status`

返回MQTT连接状态。

## 编程接口

### 注入MqttService

```java
@Autowired
private MqttService mqttService;
```

### 发布消息

```java
// 使用默认QoS和retained设置
mqttService.publishMessage("dam/data/sensor1", "{\"value\": 123}");

// 指定QoS和retained设置
mqttService.publishMessage("dam/alarm/critical", "设备故障", 2, true);
```

### 订阅主题

```java
// 使用默认QoS
mqttService.subscribe("dam/data/+");

// 指定QoS
mqttService.subscribe("dam/alarm/#", 2);
```

### 取消订阅

```java
mqttService.unsubscribe("dam/data/sensor1");
```

### 获取可用主题

```java
List<String> topics = mqttService.getAvailableTopics();
```

### 检查连接状态

```java
boolean connected = mqttService.isConnected();
```

## 消息处理

系统会自动处理接收到的MQTT消息，根据主题前缀进行分类：

- `dam/data/*` - 数据消息
- `dam/alarm/*` - 告警消息  
- `dam/command/*` - 命令消息
- `dam/status/*` - 状态消息

可以在`MqttMessageHandler`类中自定义消息处理逻辑。

## 主题规范

建议使用以下主题命名规范：

- `dam/data/{设备ID}` - 设备数据上报
- `dam/alarm/{设备ID}` - 设备告警信息
- `dam/command/{设备ID}` - 设备控制命令
- `dam/status/{设备ID}` - 设备状态信息

支持MQTT通配符：
- `+` - 单级通配符
- `#` - 多级通配符

## 权限配置

API接口需要相应权限：
- `dam:mqtt:publish` - 发布消息权限
- `dam:mqtt:subscribe` - 订阅主题权限
- `dam:mqtt:query` - 查询状态权限

## 测试

运行测试类验证功能：

```bash
mvn test -Dtest=MqttServiceTest
```

## 注意事项

1. 确保MQTT Broker服务正常运行
2. 检查网络连接和防火墙设置
3. 根据实际环境调整连接参数
4. 监控连接状态和消息处理日志
5. 合理设置QoS等级以平衡性能和可靠性

## 故障排查

1. **连接失败**：检查Broker地址、端口、用户名密码
2. **消息丢失**：检查QoS设置和网络稳定性
3. **订阅失败**：检查主题格式和权限设置
4. **性能问题**：调整连接池和超时参数
