package cn.powerchina.bjy.link.dam.config;

import org.eclipse.paho.client.mqttv3.MqttConnectOptions;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.integration.annotation.ServiceActivator;
import org.springframework.integration.channel.DirectChannel;
import org.springframework.integration.core.MessageProducer;
import org.springframework.integration.mqtt.core.DefaultMqttPahoClientFactory;
import org.springframework.integration.mqtt.core.MqttPahoClientFactory;
import org.springframework.integration.mqtt.inbound.MqttPahoMessageDrivenChannelAdapter;
import org.springframework.integration.mqtt.outbound.MqttPahoMessageHandler;
import org.springframework.integration.mqtt.support.DefaultPahoMessageConverter;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.MessageHandler;

import java.util.UUID;

/**
 * @Description: MQTT配置类
 * @Author: AI Assistant
 * @CreateDate: 2025/7/11
 */
@Configuration
public class MqttConfig {

    @Value("${mqtt.broker.url:tcp://localhost:1883}")
    private String brokerUrl;

    @Value("${mqtt.client.id:dam-client}")
    private String clientId;

    @Value("${mqtt.username:}")
    private String username;

    @Value("${mqtt.password:}")
    private String password;

    @Value("${mqtt.connection.timeout:10}")
    private int connectionTimeout;

    @Value("${mqtt.keep.alive.interval:60}")
    private int keepAliveInterval;

    @Value("${mqtt.clean.session:true}")
    private boolean cleanSession;

    @Value("${mqtt.automatic.reconnect:true}")
    private boolean automaticReconnect;

    @Value("${mqtt.default.topic:dam/default}")
    private String defaultTopic;

    @Value("${mqtt.default.qos:1}")
    private int defaultQos;

    @Value("${mqtt.default.retained:false}")
    private boolean defaultRetained;

    @Value("${mqtt.default.subscribe.qos:1}")
    private int defaultSubscribeQos;

    @Value("${mqtt.auto.subscribe.topics:}")
    private String autoSubscribeTopics;

    /**
     * MQTT客户端工厂
     */
    @Bean
    public MqttPahoClientFactory mqttClientFactory() {
        DefaultMqttPahoClientFactory factory = new DefaultMqttPahoClientFactory();
        MqttConnectOptions options = new MqttConnectOptions();
        
        // 设置服务器地址
        options.setServerURIs(brokerUrl.split(","));
        
        // 设置用户名和密码
        if (username != null && !username.trim().isEmpty()) {
            options.setUserName(username);
        }
        if (password != null && !password.trim().isEmpty()) {
            options.setPassword(password.toCharArray());
        }
        
        // 设置连接选项
        options.setCleanSession(cleanSession);
        options.setAutomaticReconnect(automaticReconnect);
        options.setConnectionTimeout(connectionTimeout);
        options.setKeepAliveInterval(keepAliveInterval);
        
        factory.setConnectionOptions(options);
        return factory;
    }

    /**
     * MQTT输入通道
     */
    @Bean
    public MessageChannel mqttInputChannel() {
        return new DirectChannel();
    }

    /**
     * MQTT输出通道
     */
    @Bean
    public MessageChannel mqttOutboundChannel() {
        return new DirectChannel();
    }

    /**
     * MQTT消息生产者（用于接收消息）
     */
    @Bean
    public MessageProducer inbound() {
        String fullClientId = clientId + "-inbound-" + UUID.randomUUID().toString();
        MqttPahoMessageDrivenChannelAdapter adapter =
                new MqttPahoMessageDrivenChannelAdapter(fullClientId, mqttClientFactory());
        adapter.setCompletionTimeout(5000);
        adapter.setConverter(new DefaultPahoMessageConverter());
        adapter.setQos(defaultSubscribeQos);
        adapter.setOutputChannel(mqttInputChannel());
        return adapter;
    }

    /**
     * MQTT消息处理器（用于发送消息）
     */
    @Bean
    @ServiceActivator(inputChannel = "mqttOutboundChannel")
    public MessageHandler mqttOutbound() {
        String fullClientId = clientId + "-outbound-" + UUID.randomUUID().toString();
        MqttPahoMessageHandler messageHandler = new MqttPahoMessageHandler(fullClientId, mqttClientFactory());
        messageHandler.setAsync(true);
        messageHandler.setDefaultTopic(defaultTopic);
        messageHandler.setDefaultQos(defaultQos);
        messageHandler.setDefaultRetained(defaultRetained);
        return messageHandler;
    }

    // Getter方法
    public String getBrokerUrl() { return brokerUrl; }
    public String getClientId() { return clientId; }
    public String getUsername() { return username; }
    public String getPassword() { return password; }
    public int getConnectionTimeout() { return connectionTimeout; }
    public int getKeepAliveInterval() { return keepAliveInterval; }
    public boolean isCleanSession() { return cleanSession; }
    public boolean isAutomaticReconnect() { return automaticReconnect; }
    public String getDefaultTopic() { return defaultTopic; }
    public int getDefaultQos() { return defaultQos; }
    public boolean isDefaultRetained() { return defaultRetained; }
    public int getDefaultSubscribeQos() { return defaultSubscribeQos; }
    public String getAutoSubscribeTopics() { return autoSubscribeTopics; }

    /**
     * 获取完整的客户端ID
     */
    public String getFullClientId() {
        return clientId + "-" + UUID.randomUUID().toString();
    }
}
