package cn.powerchina.bjy.link.dam.service.mqtt;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * @Description: MQTT连接监控器
 * @Author: AI Assistant
 * @CreateDate: 2025/7/11
 */
@Component
@Slf4j
public class MqttConnectionMonitor {

    @Autowired
    private MqttService mqttService;

    /**
     * 定期检查MQTT连接状态
     * 每30秒检查一次
     */
    @Scheduled(fixedRate = 30000)
    public void checkConnectionStatus() {
        try {
            boolean connected = mqttService.isConnected();
            if (!connected) {
                log.warn("MQTT连接已断开，尝试重新连接...");
                // 这里可以添加重连逻辑或发送告警
            } else {
                log.debug("MQTT连接状态正常");
            }
        } catch (Exception e) {
            log.error("检查MQTT连接状态时发生错误", e);
        }
    }

    /**
     * 定期发送心跳消息
     * 每60秒发送一次
     */
    @Scheduled(fixedRate = 60000)
    public void sendHeartbeat() {
        try {
            if (mqttService.isConnected()) {
                String heartbeatMessage = "{\"type\":\"heartbeat\",\"timestamp\":" + System.currentTimeMillis() + "}";
                mqttService.publishMessage("dam/heartbeat", heartbeatMessage);
                log.debug("发送MQTT心跳消息");
            }
        } catch (Exception e) {
            log.error("发送MQTT心跳消息失败", e);
        }
    }
}
