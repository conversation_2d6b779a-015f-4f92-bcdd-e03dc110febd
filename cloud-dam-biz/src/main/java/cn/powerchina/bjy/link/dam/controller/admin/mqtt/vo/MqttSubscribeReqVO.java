package cn.powerchina.bjy.link.dam.controller.admin.mqtt.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;

/**
 * @Description: MQTT主题订阅请求VO
 * @Author: AI Assistant
 * @CreateDate: 2025/7/11
 */
@Schema(description = "管理后台 - MQTT主题订阅请求 VO")
@Data
public class MqttSubscribeReqVO {

    @Schema(description = "主题", requiredMode = Schema.RequiredMode.REQUIRED, example = "dam/data/+")
    @NotBlank(message = "主题不能为空")
    private String topic;

    @Schema(description = "QoS等级 (0, 1, 2)", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "1")
    @Min(value = 0, message = "QoS等级最小为0")
    @Max(value = 2, message = "QoS等级最大为2")
    private Integer qos = 1;
}
