# 改进的MQTT配置示例
mqtt:
  broker:
    url: tcp://10.216.3.5:2883  # MQTT Broker地址，支持多个地址用逗号分隔
  client:
    id: dam-client  # 客户端ID前缀，实际使用时会添加UUID后缀
  username: f5e9ddb796ad47039a21d05477f62d82  # MQTT用户名，如果不需要认证可以留空
  password: 877fc31bf48e4498a8fc38593d4fb3d2  # MQTT密码，如果不需要认证可以留空
  connection:
    timeout: 30  # 连接超时时间（秒），增加超时时间提高稳定性
  keep:
    alive:
      interval: 60  # 心跳间隔（秒）
  clean:
    session: true  # 是否清除会话
  automatic:
    reconnect: true  # 是否自动重连
  max:
    inflight: 1000  # 最大未确认消息数
  recovery:
    interval: 10000  # 恢复间隔（毫秒）
  default:
    topic: dam/default  # 默认发布主题
    qos: 1  # 默认QoS等级
    retained: false  # 默认是否保留消息
    subscribe:
      qos: 1  # 默认订阅QoS等级
  auto:
    subscribe:
      topics: dam/default  # 自动订阅的主题，多个主题用逗号分隔

# 启用定时任务支持
spring:
  task:
    scheduling:
      enabled: true
      pool:
        size: 5  # 定时任务线程池大小

# 日志配置
logging:
  level:
    cn.powerchina.bjy.link.dam.service.mqtt: DEBUG
    org.springframework.integration.mqtt: DEBUG
    org.eclipse.paho.client.mqttv3: DEBUG
