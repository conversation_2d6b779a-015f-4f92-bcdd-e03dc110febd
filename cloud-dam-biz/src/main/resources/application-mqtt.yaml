# MQTT配置示例
mqtt:
  broker:
    url: tcp://localhost:1883  # MQTT Broker地址，支持多个地址用逗号分隔
  client:
    id: dam-client  # 客户端ID前缀，实际使用时会添加UUID后缀
  username:  # MQTT用户名，如果不需要认证可以留空
  password:  # MQTT密码，如果不需要认证可以留空
  connection:
    timeout: 10  # 连接超时时间（秒）
  keep:
    alive:
      interval: 60  # 心跳间隔（秒）
  clean:
    session: true  # 是否清除会话
  automatic:
    reconnect: true  # 是否自动重连
  default:
    topic: dam/default  # 默认发布主题
    qos: 1  # 默认QoS等级
    retained: false  # 默认是否保留消息
    subscribe:
      qos: 1  # 默认订阅QoS等级
  auto:
    subscribe:
      topics: dam/data,dam/alarm  # 自动订阅的主题，多个主题用逗号分隔
