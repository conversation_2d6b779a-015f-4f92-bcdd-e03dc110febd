package cn.powerchina.bjy.link.dam.service.mqtt;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.List;

/**
 * @Description: MQTT服务测试类
 * @Author: AI Assistant
 * @CreateDate: 2025/7/11
 */
@SpringBootTest
@ActiveProfiles("test")
@Slf4j
public class MqttServiceTest {

    @Autowired
    private MqttService mqttService;

    @Test
    public void testPublishMessage() {
        try {
            String topic = "dam/test/message";
            String message = "{\"test\": \"Hello MQTT\", \"timestamp\": " + System.currentTimeMillis() + "}";
            
            log.info("开始测试发布MQTT消息...");
            mqttService.publishMessage(topic, message);
            log.info("MQTT消息发布测试完成");
            
            // 等待一段时间确保消息发送完成
            Thread.sleep(1000);
            
        } catch (Exception e) {
            log.error("测试发布MQTT消息失败", e);
        }
    }

    @Test
    public void testSubscribe() {
        try {
            String topic = "dam/test/+";
            
            log.info("开始测试订阅MQTT主题...");
            mqttService.subscribe(topic);
            log.info("MQTT主题订阅测试完成");
            
            // 等待一段时间确保订阅完成
            Thread.sleep(2000);
            
        } catch (Exception e) {
            log.error("测试订阅MQTT主题失败", e);
        }
    }

    @Test
    public void testGetAvailableTopics() {
        try {
            log.info("开始测试获取可用主题...");
            List<String> topics = mqttService.getAvailableTopics();
            log.info("获取到的可用主题: {}", topics);
            log.info("可用主题获取测试完成");
            
        } catch (Exception e) {
            log.error("测试获取可用主题失败", e);
        }
    }

    @Test
    public void testConnectionStatus() {
        try {
            log.info("开始测试连接状态检查...");
            boolean connected = mqttService.isConnected();
            log.info("MQTT连接状态: {}", connected ? "已连接" : "未连接");
            log.info("连接状态检查测试完成");
            
        } catch (Exception e) {
            log.error("测试连接状态检查失败", e);
        }
    }

    @Test
    public void testSubscribeToAllAvailableTopics() {
        try {
            log.info("开始测试订阅所有可用主题...");
            mqttService.subscribeToAllAvailableTopics();
            log.info("订阅所有可用主题测试完成");
            
            // 等待一段时间确保订阅完成
            Thread.sleep(3000);
            
        } catch (Exception e) {
            log.error("测试订阅所有可用主题失败", e);
        }
    }

    @Test
    public void testPublishAndReceive() {
        try {
            String topic = "dam/test/pubsub";
            String message = "{\"action\": \"test\", \"data\": \"publish and receive test\", \"timestamp\": " + System.currentTimeMillis() + "}";
            
            log.info("开始测试发布和接收消息...");
            
            // 先订阅主题
            mqttService.subscribe(topic);
            Thread.sleep(1000);
            
            // 然后发布消息
            mqttService.publishMessage(topic, message);
            
            // 等待消息处理
            Thread.sleep(2000);
            
            log.info("发布和接收消息测试完成");
            
        } catch (Exception e) {
            log.error("测试发布和接收消息失败", e);
        }
    }
}
